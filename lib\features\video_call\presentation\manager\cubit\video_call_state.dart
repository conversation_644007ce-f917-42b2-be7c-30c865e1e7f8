part of 'video_call_cubit.dart';

abstract class VideoCallState extends Equatable {
  @override
  List<Object?> get props => [];
}

class VideoCallInitial extends VideoCallState {}

class VideoCallLoading extends VideoCallState {}

class Video<PERSON>allJoined extends VideoCallState {
  final Call call;
  final String userId;

  VideoCallJoined({required this.call, required this.userId});

  @override
  List<Object?> get props => [call, userId];
}

class VideoCallError extends Video<PERSON>allState {
  final String message;

  VideoCallError({required this.message});

  @override
  List<Object?> get props => [message];
}

class VideoCallLeft extends Video<PERSON>allState {}